package gateways

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Provides access to the Postgres DB used by the new Design service.
// The current schema of this DB is defined in `frameworks/db/schema.sql`.
// TODO: add a mechanism to mark renders outdated when designs get updated.
type RelationalDb struct {
	db *pgxpool.Pool
}

func NewRelationalDb(db *pgxpool.Pool) *RelationalDb {
	return &RelationalDb{db: db}
}

func (r *RelationalDb) Close() {
	r.db.Close()
}

func (r *RelationalDb) IdsOfProjectsWithDesigns(ctx context.Context) ([]entities.ProjectId, error) {
	projectIds := make([]entities.ProjectId, 0)
	rows, err := r.db.Query(ctx, "SELECT DISTINCT project_id FROM designs.room_designs")
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	defer rows.Close()
	for rows.Next() {
		var projectId entities.ProjectId
		if err := rows.Scan(&projectId); err != nil {
			return nil, fmt.Errorf("scan failed: %w", err)
		}
		projectIds = append(projectIds, projectId)
	}
	return projectIds, nil
}

func (r *RelationalDb) InsertPreset(ctx context.Context, preset usecases.Preset) (string, error) {
	if preset.Id == "" {
		preset.Id = uuid.NewString()
		log.Printf("Generated new ID %s for preset", preset.Id)
	}

	tx, err := r.db.Begin(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// 1. Insert the design data (room_designs, default_products, render_prefs)
	var zeroUUID uuid.UUID
	design := preset.Design
	if design.ID == zeroUUID {
		design.ID = uuid.New()
		log.Printf("Generated new design ID %s for preset", design.ID)
	}

	roomDesignQuery := `
		INSERT INTO design.room_designs (
			id, project_id, status, style, color_scheme, title, description
		) VALUES (
			@id, @project_id, @status, @style, @color_scheme, @title, @description
		)`
	_, err = tx.Exec(ctx, roomDesignQuery, pgx.NamedArgs{
		"id":           design.ID,
		"project_id":   design.ProjectID,
		"status":       design.Status,
		"style":        design.Style,
		"color_scheme": design.ColorScheme,
		"title":        design.Title,
		"description":  design.Description,
	})
	if err != nil {
		return "", fmt.Errorf("failed to insert into room_designs: %w", err)
	}

	productsQuery := `
		INSERT INTO design.default_products (
			room_design_id, floor_tile, floor_tile_pattern, toilet, vanity, faucet, mirror,
			lighting, paint, shelving, wall_tile_placement, wall_tile, wall_tile_pattern,
			wallpaper_placement, wallpaper, shower_system, shower_floor_tile,
			shower_floor_tile_pattern, shower_wall_tile, shower_wall_tile_pattern,
			shower_short_wall_tile, shower_glass, niche_tile, tub, tub_filler, tub_door
		) VALUES (
			@room_design_id, @floor_tile, @floor_tile_pattern, @toilet, @vanity, @faucet, @mirror,
			@lighting, @paint, @shelving, @wall_tile_placement, @wall_tile, @wall_tile_pattern,
			@wallpaper_placement, @wallpaper, @shower_system, @shower_floor_tile,
			@shower_floor_tile_pattern, @shower_wall_tile, @shower_wall_tile_pattern,
			@shower_short_wall_tile, @shower_glass, @niche_tile, @tub, @tub_filler, @tub_door
		)`
	_, err = tx.Exec(ctx, productsQuery, pgx.NamedArgs{
		"room_design_id":            design.ID,
		"floor_tile":                design.FloorTile,
		"floor_tile_pattern":        design.FloorTilePattern,
		"toilet":                    design.Toilet,
		"vanity":                    design.Vanity,
		"faucet":                    design.Faucet,
		"mirror":                    design.Mirror,
		"lighting":                  design.Lighting,
		"paint":                     design.Paint,
		"shelving":                  design.Shelving,
		"wall_tile_placement":       design.WallTilePlacement,
		"wall_tile":                 design.WallTile,
		"wall_tile_pattern":         design.WallTilePattern,
		"wallpaper_placement":       design.WallpaperPlacement,
		"wallpaper":                 design.Wallpaper,
		"shower_system":             design.ShowerSystem,
		"shower_floor_tile":         design.ShowerFloorTile,
		"shower_floor_tile_pattern": design.ShowerFloorTilePattern,
		"shower_wall_tile":          design.ShowerWallTile,
		"shower_wall_tile_pattern":  design.ShowerWallTilePattern,
		"shower_short_wall_tile":    design.ShowerShortWallTile,
		"shower_glass":              design.ShowerGlass,
		"niche_tile":                design.NicheTile,
		"tub":                       design.Tub,
		"tub_filler":                design.TubFiller,
		"tub_door":                  design.TubDoor,
	})
	if err != nil {
		return "", fmt.Errorf("failed to insert into default_products: %w", err)
	}

	prefsQuery := `
		INSERT INTO design.render_prefs (
			room_design_id, shower_glass_visible, tub_door_visible, niches_visible
		) VALUES (
			@room_design_id, @shower_glass_visible, @tub_door_visible, @niches_visible
		)`
	_, err = tx.Exec(ctx, prefsQuery, pgx.NamedArgs{
		"room_design_id":       design.ID,
		"shower_glass_visible": design.ShowerGlassVisible,
		"tub_door_visible":     design.TubDoorVisible,
		"niches_visible":       design.NichesVisible,
	})
	if err != nil {
		return "", fmt.Errorf("failed to insert into render_prefs: %w", err)
	}

	// 2. Insert the rendition
	rendition := preset.Rendition
	if rendition.Id == zeroUUID {
		rendition.Id = uuid.New()
		log.Printf("Generated new rendition ID %s for preset", rendition.Id)
	}

	renditionQuery := `
		INSERT INTO public.renditions (id, room_design_id, status, url) 
		VALUES ( @id, @room_design_id, @status, @url)`

	var renditionURL *string
	if rendition.URL.String() != "" {
		urlStr := rendition.URL.String()
		renditionURL = &urlStr
	}

	_, err = tx.Exec(ctx, renditionQuery, pgx.NamedArgs{
		"id":             rendition.Id,
		"room_design_id": design.ID,
		"status":         rendition.Status,
		"url":            renditionURL,
	})
	if err != nil {
		return "", fmt.Errorf("failed to insert into renditions: %w", err)
	}

	if err = tx.Commit(ctx); err != nil {
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return preset.Id, nil
}

// UpsertDesign persists a Design entity to the database.
// It uses a transaction to ensure atomicity across multiple table insertions.
func (r *RelationalDb) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	var zeroUUID uuid.UUID
	if design.ID == zeroUUID {
		design.ID = uuid.New()
		log.Printf("Generated new ID %s for design", design.ID)
	}
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return zeroUUID, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx) // Rollback is a no-op if the transaction is committed.

	// 1. Insert into room_designs
	roomDesignQuery := `
		MERGE INTO design.room_designs 
		USING (VALUES (@id, @project_id, @status, @style, @color_scheme, @title, @description))
			AS source (id, project_id, status, style, color_scheme, title, description)
		ON design.room_designs.id = source.id::uuid
		WHEN MATCHED THEN UPDATE SET
			project_id = source.project_id,
			status = source.status,
			style = source.style::style_enum,
			color_scheme = source.color_scheme::color_scheme_enum,
			title = source.title,
			description = source.description,
			updated_at = NOW()
		WHEN NOT MATCHED THEN
			INSERT (id, project_id, status, style, color_scheme, title, description)
			VALUES (source.id::uuid, source.project_id, source.status,
					source.style::style_enum, source.color_scheme::color_scheme_enum,
					source.title, source.description
			)
		`
	_, err = tx.Exec(ctx, roomDesignQuery, pgx.NamedArgs{
		"id":           design.ID,
		"project_id":   design.ProjectID,
		"status":       design.Status,
		"style":        design.Style,
		"color_scheme": design.ColorScheme,
		"title":        design.Title,
		"description":  design.Description,
	})
	if err != nil {
		return design.ID, fmt.Errorf("failed to insert into room_designs: %w", err)
	}

	// 2. Insert into default_products
	productsQuery := `
		INSERT INTO design.default_products (
			room_design_id, floor_tile, floor_tile_pattern, toilet, vanity, faucet, mirror,
			lighting, paint, shelving, wall_tile_placement, wall_tile, wall_tile_pattern,
			wallpaper_placement, wallpaper, shower_system, shower_floor_tile,
			shower_floor_tile_pattern, shower_wall_tile, shower_wall_tile_pattern,
			shower_short_wall_tile, shower_glass, niche_tile, tub, tub_filler, tub_door
		) VALUES (
			@room_design_id, @floor_tile, @floor_tile_pattern, @toilet, @vanity, @faucet, @mirror,
			@lighting, @paint, @shelving, @wall_tile_placement, @wall_tile, @wall_tile_pattern,
			@wallpaper_placement, @wallpaper, @shower_system, @shower_floor_tile,
			@shower_floor_tile_pattern, @shower_wall_tile, @shower_wall_tile_pattern,
			@shower_short_wall_tile, @shower_glass, @niche_tile, @tub, @tub_filler, @tub_door
		)
		ON CONFLICT (room_design_id) DO UPDATE SET 
			floor_tile = EXCLUDED.floor_tile, floor_tile_pattern = EXCLUDED.floor_tile_pattern,
			toilet = EXCLUDED.toilet, paint = EXCLUDED.paint,
			vanity = EXCLUDED.vanity, faucet = EXCLUDED.faucet, mirror = EXCLUDED.mirror,
			lighting = EXCLUDED.lighting, shelving = EXCLUDED.shelving,
			wall_tile_placement = EXCLUDED.wall_tile_placement,
			wall_tile = EXCLUDED.wall_tile, wall_tile_pattern = EXCLUDED.wall_tile_pattern,
			wallpaper_placement = EXCLUDED.wallpaper_placement, wallpaper = EXCLUDED.wallpaper,
			shower_floor_tile = EXCLUDED.shower_floor_tile,
			shower_floor_tile_pattern = EXCLUDED.shower_floor_tile_pattern,
			shower_wall_tile = EXCLUDED.shower_wall_tile,
			shower_wall_tile_pattern = EXCLUDED.shower_wall_tile_pattern,
			shower_short_wall_tile = EXCLUDED.shower_short_wall_tile,
			niche_tile = EXCLUDED.niche_tile,
			shower_system = EXCLUDED.shower_system, shower_glass = EXCLUDED.shower_glass,
			tub = EXCLUDED.tub, tub_filler = EXCLUDED.tub_filler, tub_door = EXCLUDED.tub_door
		`
	_, err = tx.Exec(ctx, productsQuery, pgx.NamedArgs{
		"room_design_id":            design.ID,
		"floor_tile":                design.FloorTile,
		"floor_tile_pattern":        design.FloorTilePattern,
		"toilet":                    design.Toilet,
		"vanity":                    design.Vanity,
		"faucet":                    design.Faucet,
		"mirror":                    design.Mirror,
		"lighting":                  design.Lighting,
		"paint":                     design.Paint,
		"shelving":                  design.Shelving,
		"wall_tile_placement":       design.WallTilePlacement,
		"wall_tile":                 design.WallTile,
		"wall_tile_pattern":         design.WallTilePattern,
		"wallpaper_placement":       design.WallpaperPlacement,
		"wallpaper":                 design.Wallpaper,
		"shower_system":             design.ShowerSystem,
		"shower_floor_tile":         design.ShowerFloorTile,
		"shower_floor_tile_pattern": design.ShowerFloorTilePattern,
		"shower_wall_tile":          design.ShowerWallTile,
		"shower_wall_tile_pattern":  design.ShowerWallTilePattern,
		"shower_short_wall_tile":    design.ShowerShortWallTile,
		"shower_glass":              design.ShowerGlass,
		"niche_tile":                design.NicheTile,
		"tub":                       design.Tub,
		"tub_filler":                design.TubFiller,
		"tub_door":                  design.TubDoor,
	})
	if err != nil {
		return design.ID, fmt.Errorf("failed to insert into default_products: %w", err)
	}

	// 3. Insert into render_prefs
	prefsQuery := `
		INSERT INTO design.render_prefs (
		    room_design_id, shower_glass_visible, tub_door_visible, niches_visible
		)
		VALUES (@room_design_id, @shower_glass_visible, @tub_door_visible, @niches_visible)
		ON CONFLICT (room_design_id)
		DO UPDATE SET
		    shower_glass_visible = EXCLUDED.shower_glass_visible,
		    tub_door_visible = EXCLUDED.tub_door_visible,
		    niches_visible = EXCLUDED.niches_visible
		`
	_, err = tx.Exec(ctx, prefsQuery, pgx.NamedArgs{
		"room_design_id":       design.ID,
		"shower_glass_visible": design.ShowerGlassVisible,
		"tub_door_visible":     design.TubDoorVisible,
		"niches_visible":       design.NichesVisible,
	})
	if err != nil {
		return design.ID, fmt.Errorf("failed to upsert into render_prefs: %w", err)
	}

	if err = tx.Commit(ctx); err != nil {
		return design.ID, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return design.ID, nil
}

// ReadDesign fetches a single Design entity from the database by its ID.
// It joins all related tables to construct the complete Design object.
func (r *RelationalDb) ReadDesign(ctx context.Context, id uuid.UUID) (usecases.Design, error) {
	query := fmt.Sprintf("%s WHERE rd.id = $1", readDesignBaseQuery)
	row := r.db.QueryRow(ctx, query, id)
	if usecases.IsNil(row) {
		return usecases.Design{}, fmt.Errorf("no design found with id %s", id)
	}
	return scanDesign(row)
}

func (r *RelationalDb) FindPresetByLegacyId(ctx context.Context, templateId string) (usecases.Preset, error) {
	// Query to join legacy_lookup -> renditions -> room_designs and get all design data
	query := fmt.Sprintf(`
		SELECT
			ll.id as legacy_id,
			ll.template_id,
			r.id as rendition_id, r.created_at as rendition_created_at, r.updated_at as rendition_updated_at,
			r.status as rendition_status, r.url as rendition_url,
			%s
		FROM public.legacy_lookup ll
		INNER JOIN public.renditions r ON ll.rendition_id = r.id
		INNER JOIN design.room_designs rd ON r.room_design_id = rd.id
		INNER JOIN design.default_products dp ON rd.id = dp.room_design_id
		INNER JOIN design.render_prefs rp ON rd.id = rp.room_design_id
		WHERE ll.id = $1`,
		designFields)

	row := r.db.QueryRow(ctx, query, templateId)

	// Scan the legacy lookup and rendition data first
	var legacyId string
	var templateUUID uuid.UUID
	var renditionId uuid.UUID
	var renditionCreatedAt, renditionUpdatedAt time.Time
	var renditionStatus string
	var renditionURL sql.NullString

	// Then scan the design data using the same pattern as scanDesign
	var rd RoomDesignModel
	var dp DefaultProductsModel
	var rp RenderPrefsModel

	err := row.Scan(
		&legacyId, &templateUUID,
		&renditionId, &renditionCreatedAt, &renditionUpdatedAt, &renditionStatus, &renditionURL,
		// Design fields (same order as in scanDesign)
		&rd.ID, &rd.ProjectID, &rd.CreatedAt, &rd.UpdatedAt,
		&rd.Status, &rd.Style, &rd.ColorScheme, &rd.Title, &rd.Description,
		&dp.FloorTile, &dp.FloorTilePattern, &dp.Toilet, &dp.Vanity, &dp.Faucet,
		&dp.Mirror, &dp.Lighting, &dp.Paint, &dp.Shelving,
		&dp.WallTilePlacement, &dp.WallTile, &dp.WallTilePattern,
		&dp.WallpaperPlacement, &dp.Wallpaper,
		&dp.ShowerSystem, &dp.ShowerFloorTile, &dp.ShowerFloorTilePattern,
		&dp.ShowerWallTile, &dp.ShowerWallTilePattern, &dp.ShowerShortWallTile,
		&dp.ShowerGlass, &dp.NicheTile, &dp.Tub, &dp.TubFiller, &dp.TubDoor,
		&rp.ShowerGlassVisible, &rp.TubDoorVisible, &rp.NichesVisible,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return usecases.Preset{}, fmt.Errorf("no preset found with legacy ID %s", templateId)
		}
		return usecases.Preset{}, fmt.Errorf("failed to scan preset row: %w", err)
	}

	design := newDesignFromModels(&rd, &dp, &rp)

	// Construct the Rendition
	var renditionURLParsed url.URL
	if renditionURL.Valid && renditionURL.String != "" {
		parsed, err := url.Parse(renditionURL.String)
		if err != nil {
			return usecases.Preset{}, fmt.Errorf("failed to parse rendition URL: %w", err)
		}
		renditionURLParsed = *parsed
	}

	rendition := entities.Rendition{
		Id:        renditionId,
		CreatedAt: renditionCreatedAt,
		UpdatedAt: renditionUpdatedAt,
		Status:    entities.RenditionStatus(renditionStatus),
		URL:       renditionURLParsed,
	}

	preset := usecases.Preset{
		Id:         legacyId,
		TemplateId: templateUUID,
		CreatedAt:  design.Created,
		UpdatedAt:  design.LastUpdated,
		Design:     design,
		Rendition:  rendition,
		RoomLayout: entities.RoomLayout{},
	}

	return preset, nil
}

// DesignsForProject retrieves all designs associated with a given project ID.
func (r *RelationalDb) DesignsForProject(ctx context.Context, projectID entities.ProjectId) ([]usecases.Design, error) {
	query := fmt.Sprintf("%s WHERE rd.project_id = $1 ORDER BY rd.updated_at DESC", readDesignBaseQuery)
	rows, err := r.db.Query(ctx, query, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to query designs by project id: %w", err)
	}
	defer rows.Close()

	var designs []usecases.Design
	for rows.Next() {
		design, err := scanDesign(rows)
		if err != nil {
			return nil, err
		}
		designs = append(designs, design)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during rows iteration: %w", err)
	}
	return designs, nil
}

// DesignsByProject retrieves all designs associated with the given project IDs.
func (r *RelationalDb) DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (map[entities.ProjectId][]usecases.Design, []error, error) {
	var errors []error
	projectDesigns := make(map[entities.ProjectId][]usecases.Design)
	query := fmt.Sprintf("%s WHERE rd.project_id = ANY($1) ORDER BY rd.project_id, rd.updated_at DESC",
		readDesignBaseQuery)
	rows, err := r.db.Query(ctx, query, projectIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to query designs by project ids (%v): %w", projectIDs, err)
	}
	defer rows.Close()

	for rows.Next() {
		design, err := scanDesign(rows)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		designs := projectDesigns[design.ProjectID]
		designs = append(designs, design)
		projectDesigns[design.ProjectID] = designs
	}
	if err = rows.Err(); err != nil {
		return nil, nil, fmt.Errorf("error during rows iteration: %w", err)
	}

	if len(projectDesigns) < len(projectIDs) {
		log.Printf("Warning: Some projects have no designs.")
	}
	return projectDesigns, errors, nil
}

// DeleteDesign removes a Design entity and its associated data from the database.
// It relies on the ON DELETE CASCADE constraint in the database schema.
// TODO: consider offering only *soft* deletes unless we build an event-sourcing
// that would allows us to reverse deletions.
func (r *RelationalDb) DeleteDesign(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM design.room_designs WHERE id = $1`

	cmdTag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to execute delete command: %w", err)
	}

	if cmdTag.RowsAffected() == 0 {
		return fmt.Errorf("no design found with id %s to delete", id)
	}

	return nil
}
