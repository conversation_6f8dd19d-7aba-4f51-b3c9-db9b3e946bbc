package usecases

import (
	"net/url"
	"time"

	"github.com/google/uuid"
)

// TODO: make the JSON field name capitalization consistently camelCase.
type Template struct {
	ID        uuid.UUID `json:"id"`
	UpdatedAt time.Time `json:"updatedAt"`

	ColorScheme ColorScheme `json:"color_scheme"`
	Style       Style       `json:"style"`
	Name        string      `json:"name"`
	ImageURL    url.URL     `json:"image_url"`

	Description          string    `json:"description"`
	Inspiration          string    `json:"inspiration"`
	Atmosphere           []string  `json:"atmosphere"`
	ColorPalette         []string  `json:"color_palette"`
	MaterialPalette      []string  `json:"material_palette"`
	HighlightedBrandUrls []url.URL `json:"highlighted_brand_urls"`

	FixedProductSelections
	ProductSelectionOptions
	TemplateProvenance
}

type ProductSelectionOptions struct {
	AlcoveTub       uuid.UUID `json:"alcoveTub"`
	FreestandingTub uuid.UUID `json:"freestandingTub"`

	ShowerGlassFixed   uuid.UUID `json:"showerGlassFixed"`
	ShowerGlassSliding uuid.UUID `json:"showerGlassSliding"`

	ShowerSystemCombo uuid.UUID `json:"showerSystemFull"`
	ShowerSystemSolo  uuid.UUID `json:"showerSystemShower"`

	TubDoorFixed   uuid.UUID `json:"tubDoorFixed"`
	TubDoorSliding uuid.UUID `json:"tubDoorSliding"`
}

type TemplateProvenance struct {
	LightingBrand *string `json:"lighting_brand,omitempty"`
	PlumbingBrand *string `json:"plumbing_brand,omitempty"`
	ToiletBrand   *string `json:"toilet_brand,omitempty"`
	VanityBrand   *string `json:"vanity_brand,omitempty"`

	VanityStorage *string `json:"vanity_storage,omitempty"`
}
