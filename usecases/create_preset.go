package usecases

import (
	"context"
	"log/slog"
)

type PresetCreater struct {
	presetRepo presetRepository
	logger     *slog.Logger
}

func NewPresetCreater(presetRepo presetRepository, logger *slog.Logger) *PresetCreater {
	if IsNil(presetRepo) {
		panic("presetRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &PresetCreater{presetRepo: presetRepo, logger: logger}
}

func (dc *PresetCreater) CreatePreset(ctx context.Context, presenter PresetCreationOutcomePresenter, preset Preset) {
	if preset.Id != "" {
		dc.logger.ErrorContext(ctx, "Attempt to create preset with ID specified.", slog.String("presetId", preset.Id))
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	var err error
	if preset.Id, err = dc.presetRepo.InsertPreset(ctx, preset); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccessWithNewResource(preset)
}
