package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type GenAI interface {
	GenerateDesignTitleAndDescription(ctx context.Context, design Design) (string, string, error)
}

type designRepositoryReplica interface {
	ReadDesign(ctx context.Context, designId uuid.UUID) (Design, error)
	DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]Design, error)
	DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (map[entities.ProjectId][]Design, []error, error)
}

type designRepository interface {
	designRepositoryReplica
	UpsertDesign(ctx context.Context, design Design) (uuid.UUID, error)
	DeleteDesign(ctx context.Context, designId uuid.UUID) error
}

type presetRepositoryReplica interface {
	FindPresetByLegacyId(ctx context.Context, templateId string) (Preset, error)
}

type presetRepository interface {
	presetRepositoryReplica
	InsertPreset(ctx context.Context, preset Preset) (string, error)
}

type Presenter interface {
	PresentError(err error)
}

type DesignsPresenter interface {
	Presenter
	PresentData(ctx context.Context, data any)
	PresentDesign(ctx context.Context, design Design)
	PresentDesigns(ctx context.Context, designs []Design)
	PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]Design, errors []error)
}

type PresetPresenter interface {
	Presenter
	PresentPreset(ctx context.Context, preset Preset)
}

type OutcomePresenter interface {
	Presenter
	ConveySuccess()
}

type DesignMutationOutcomePresenter interface {
	OutcomePresenter
	ConveySuccessWithResource(design Design, status Status)
}

type PresetCreationOutcomePresenter interface {
	Presenter
	ConveySuccessWithNewResource(preset Preset)
}
